declare const _default: {
    extends: string[];
    rules: {
        '@typescript-eslint/ban-ts-comment': ["error", {
            minimumDescriptionLength: number;
        }];
        'no-array-constructor': "off";
        '@typescript-eslint/no-array-constructor': "error";
        '@typescript-eslint/no-duplicate-enum-values': "error";
        '@typescript-eslint/no-dynamic-delete': "error";
        '@typescript-eslint/no-empty-object-type': "error";
        '@typescript-eslint/no-explicit-any': "error";
        '@typescript-eslint/no-extra-non-null-assertion': "error";
        '@typescript-eslint/no-extraneous-class': "error";
        '@typescript-eslint/no-invalid-void-type': "error";
        '@typescript-eslint/no-misused-new': "error";
        '@typescript-eslint/no-namespace': "error";
        '@typescript-eslint/no-non-null-asserted-nullish-coalescing': "error";
        '@typescript-eslint/no-non-null-asserted-optional-chain': "error";
        '@typescript-eslint/no-non-null-assertion': "error";
        '@typescript-eslint/no-require-imports': "error";
        '@typescript-eslint/no-this-alias': "error";
        '@typescript-eslint/no-unnecessary-type-constraint': "error";
        '@typescript-eslint/no-unsafe-declaration-merging': "error";
        '@typescript-eslint/no-unsafe-function-type': "error";
        'no-unused-expressions': "off";
        '@typescript-eslint/no-unused-expressions': "error";
        'no-unused-vars': "off";
        '@typescript-eslint/no-unused-vars': "error";
        'no-useless-constructor': "off";
        '@typescript-eslint/no-useless-constructor': "error";
        '@typescript-eslint/no-wrapper-object-types': "error";
        '@typescript-eslint/prefer-as-const': "error";
        '@typescript-eslint/prefer-literal-enum-member': "error";
        '@typescript-eslint/prefer-namespace-keyword': "error";
        '@typescript-eslint/triple-slash-reference': "error";
        '@typescript-eslint/unified-signatures': "error";
    };
};
export = _default;
//# sourceMappingURL=strict.d.ts.map